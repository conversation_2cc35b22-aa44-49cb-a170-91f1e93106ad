'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Phone, Mail, MapPin } from 'lucide-react';
import { COMPANY_INFO, NAVIGATION_ITEMS, SOCIAL_LINKS } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface HomeHeaderProps {
  isFallbackMode?: boolean;
}

export default function HomeHeader({ isFallbackMode = false }: HomeHeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
      if (window.scrollY > 10 && !hasScrolled) {
        setHasScrolled(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasScrolled]);

  const isActivePath = (href: string) => {
    if (href === '/') return pathname === '/';
    // Handle specific conflicts between similar paths
    if (href === '/trips') {
      return pathname === '/trips' || (pathname.startsWith('/trips/') && !pathname.startsWith('/trips-photos'));
    }
    if (href === '/trips-photos') {
      return pathname.startsWith('/trips-photos');
    }
    // For other paths, use the original logic
    return pathname.startsWith(href);
  };

  return (
    <>


      {/* Main Header */}
      <motion.header
        className={cn(
          'absolute top-0 left-0 right-0 z-50 transition-all duration-500',
          isFallbackMode
            ? isScrolled
              ? 'bg-white/80 backdrop-blur-2xl shadow-2xl border-b border-white/30 fixed'
              : 'bg-transparent border-transparent'
            : isScrolled
              ? 'bg-white/80 backdrop-blur-2xl shadow-2xl border-b border-white/30 fixed'
              : 'bg-white/10 backdrop-blur-sm border-b border-white/5'
        )}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
            <div className="container-custom px-4 md:px-8 lg:px-12">
              <div className="flex items-center justify-between h-16 lg:h-20">
                {/* Logo */}
                <Link href="/" className="flex items-center">
                  <div className="relative h-16 w-16 lg:h-28 lg:w-28">
                    <Image
                      src="/images/static/logos/positive7-logo.png"
                      alt={COMPANY_INFO.name}
                      fill
                      className="object-contain"
                      priority
                    />
                  </div>
                </Link>

                {/* Desktop Navigation */}
                <nav className="hidden lg:flex items-center justify-end w-full space-x-4">
                  {NAVIGATION_ITEMS.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href as any}
                      className={cn(
                        'relative text-sm font-medium transition-all duration-300 hover:scale-105',
                        'px-2 py-1 rounded-lg hover:backdrop-blur-sm',
                        isActivePath(item.href)
                          ? isFallbackMode
                            ? 'text-coral-400 bg-gray-800/30 backdrop-blur-sm shadow-sm'
                            : 'text-primary-600 bg-white/30 backdrop-blur-sm shadow-sm'
                          : isFallbackMode
                            ? isScrolled
                              ? 'text-gray-700 hover:text-coral-400 hover:bg-white/40'
                              : 'text-white hover:text-coral-300 hover:bg-white/20'
                            : isScrolled
                              ? 'text-gray-700 hover:text-primary-600 hover:bg-white/40'
                              : 'text-white hover:text-primary-200 hover:bg-white/20'
                      )}
                    >
                      {item.name}
                      {isActivePath(item.href) && (
                        <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></span>
                      )}
                    </Link>
                  ))}
                </nav>

                {/* Mobile Menu Button */}
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className={cn(
                      'lg:hidden p-2 rounded-lg transition-all duration-300 hover:scale-105',
                      // When menu is open, ensure button is always visible with proper contrast
                      isMenuOpen
                        ? 'text-gray-700 bg-white/90 hover:bg-white hover:text-primary-600 shadow-lg backdrop-blur-sm'
                        : isFallbackMode
                          ? isScrolled
                            ? 'text-gray-700 hover:text-coral-400 hover:bg-white/50 hover:backdrop-blur-sm'
                            : 'text-white hover:text-coral-300 hover:bg-white/20 hover:backdrop-blur-sm'
                          : isScrolled
                            ? 'text-gray-700 hover:text-primary-600 hover:bg-white/50 hover:backdrop-blur-sm'
                            : 'text-white hover:text-primary-200 hover:bg-white/20 hover:backdrop-blur-sm'
                    )}
                    aria-label="Toggle menu"
                    aria-expanded={isMenuOpen}
                    aria-controls="mobile-menu"
                  >
                    {isMenuOpen ? (
                      <X className="h-6 w-6" />
                    ) : (
                      <Menu className="h-6 w-6" />
                    )}
                  </button>
                </div>
              </div>
            </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              id="mobile-menu"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className={cn(
                'lg:hidden backdrop-blur-xl border-t',
                isFallbackMode
                  ? 'bg-white/90 border-white/20'
                  : 'bg-white/90 border-white/20'
              )}
            >
              <div className="container-custom py-4">
                <nav className="flex flex-col space-y-2">
                  {NAVIGATION_ITEMS.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href as any}
                      className={cn(
                        'text-base font-medium transition-all duration-300 px-4 py-3 rounded-lg hover:scale-105',
                        isActivePath(item.href)
                          ? isFallbackMode
                            ? 'text-primary-600 bg-primary-50/80 backdrop-blur-sm'
                            : 'text-primary-600 bg-primary-50/80 backdrop-blur-sm'
                          : isFallbackMode
                            ? 'text-gray-700 hover:text-primary-600 hover:bg-white/50 hover:backdrop-blur-sm'
                            : 'text-gray-700 hover:text-primary-600 hover:bg-white/50 hover:backdrop-blur-sm'
                      )}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>
    </>
  );
} 